server:
  servlet:
    context-path: /api/v1
spring:
  profiles:
    default: dev

  datasource:
    url: ${database.url}
    username: ${database.username}
    password: ${database.password}
    hikari:
      connection-timeout: 30000
      maximum-pool-size: 10
  jpa:
    hibernate:
      ddl-auto: none
    open-in-view: false
    properties:
      hibernate:
        jdbc:
          lob:
            non_contextual_creation: true
        id:
          new_generator_mappings: true

  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.yaml
    default-schema: public
    enabled: true
    contexts: default

# The database configuration for the application
database:
  url: ***************************************
  username: username
  password: password

# The IAM configuration for the application
# they are used to admin client creating user in keycloak
iam:
  endpoint: ${IAM_ENDPOINT:http://localhost:8080}
  realm: ${IAM_REALM:smaile}
  client-id: ${IAM_CLIENT_ID:smaile-be}
  client-secret: ${IAM_CLIENT_SECRET:B7ca4tWTJdSK4g1L8yq0mi7xmISNydiB}

springdoc:
  pathsToMatch: /, /api/**
