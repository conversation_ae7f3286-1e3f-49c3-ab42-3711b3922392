server:
  servlet:
    context-path: /api/v1
spring:
  profiles:
    default: dev

  datasource:
    url: ${database.url}
    username: ${database.username}
    password: ${database.password}
    hikari:
      connection-timeout: 30000
      maximum-pool-size: 10
  jpa:
    hibernate:
      ddl-auto: none
    open-in-view: false
    properties:
      hibernate:
        jdbc:
          lob:
            non_contextual_creation: true
        id:
          new_generator_mappings: true

  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.yaml
    default-schema: public
    enabled: true
    contexts: default

# The database configuration for the application
database:
  url: ***************************************
  username: username
  password: password

# The IAM configuration for the application
# they are used to admin client creating user in keycloak
iam:
  endpoint: ${IAM_ENDPOINT:http://localhost:8080}
  realm: ${IAM_REALM:smaile}
  client-id: ${IAM_CLIENT_ID:smaile-be}
  client-secret: ${IAM_CLIENT_SECRET:B7ca4tWTJdSK4g1L8yq0mi7xmISNydiB}

springdoc:
  pathsToMatch: /, /api/**

# Security exception handling configuration
smaile:
  security:
    exceptions:
      include-stack-trace: false
      include-detailed-messages: false
      enable-logging: true
      log-level: WARN
      problem-type-base-uri: https://smaile.health/problems
      custom-headers:
        X-Content-Type-Options: nosniff
        X-Frame-Options: DENY
        X-XSS-Protection: "1; mode=block"
        Strict-Transport-Security: "max-age=31536000; includeSubDomains"
      authentication:
        status-code: 401
        title: Authentication Required
        detail: Valid authentication credentials are required to access this resource
        type: authentication-required
      access-denied:
        status-code: 403
        title: Access Denied
        detail: You do not have sufficient permissions to access this resource
        type: access-denied
