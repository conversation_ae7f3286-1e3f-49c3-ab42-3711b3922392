spring:
  datasource:
    url: ${database.url}
    username: ${database.username}
    password: ${database.password}
    hikari:
      connection-timeout: 30000
      maximum-pool-size: 5
  jpa:
    properties:
      hibernate:
        format_sql: true
        show_sql: true
        jdbc:
          batch_size: 10
  liquibase:
    contexts: development


server:
  port: ${SERVER_PORT:8000}

# The database configuration for the application
database:
  url: ***************************************
  username: username
  password: password

# The IAM configuration for the application
# they are used to admin client creating user in keycloak
iam:
  endpoint: ${IAM_ENDPOINT:http://localhost:8080}
  realm: ${IAM_REALM:smaile}
  client-id: ${IAM_CLIENT_ID:smaile-be}
  client-secret: ${IAM_CLIENT_SECRET:B7ca4tWTJdSK4g1L8yq0mi7xmISNydiB}

management:
  endpoint:
    health:
      show-details: always
  metrics:
    tags:
      environment: development

logging:
  level:
    com.smaile.health: DEBUG
    org.springframework.security: DEBUG
    liquibase: DEBUG
    root: INFO
