spring:
  datasource:
    hikari:
      connection-timeout: 20000
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      max-lifetime: 1200000
  jpa:
    properties:
      hibernate:
        format_sql: false
        show_sql: false
        jdbc:
          batch_size: 25
          order_inserts: true
          order_updates: true
  liquibase:
    contexts: production

# The database configuration for the application
database:
  url: ******************************************
  username:
  password:

# The IAM configuration for the application
# they are used to admin client creating user in keycloak
iam:
  endpoint: ${IAM_ENDPOINT:http://localhost:8080}
  realm: ${IAM_REALM:smaile}
  client-id: ${IAM_CLIENT_ID:smaile-be}
  client-secret: ${IAM_CLIENT_SECRET:B7ca4tWTJdSK4g1L8yq0mi7xmISNydiB}

management:
  endpoint:
    health:
      show-details: never
  metrics:
    tags:
      environment: production

logging:
  level:
    com.smaile.health: INFO
    org.springframework.cache: WARN
    org.springframework.security: WARN
    liquibase: WARN
    root: WARN

# Production-specific security exception handling configuration
smaile:
  security:
    exceptions:
      include-stack-trace: false
      include-detailed-messages: false
      enable-logging: true
      log-level: ERROR
      custom-headers:
        X-Content-Type-Options: nosniff
        X-Frame-Options: DENY
        X-XSS-Protection: "1; mode=block"
        Strict-Transport-Security: "max-age=31536000; includeSubDomains"
        Content-Security-Policy: "default-src 'self'"