package com.smaile.health.config;

import com.smaile.health.service.UserService;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class AuthenticationFilter implements Filter {

    private static final String X_FORWARDED_USER = "x-forwarded-smaile-user";
    private static final String X_FORWARDED_EMAIL = "x-forwarded-email";
    private static final String X_FORWARDED_PREFERRED_USERNAME = "x-forwarded-preferred-username";

    @Autowired
    private UserService userService;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;
        String kcUserId = httpRequest.getHeader(X_FORWARDED_USER);
        String kcEmail = httpRequest.getHeader(X_FORWARDED_EMAIL);
        String kcPreferredUsername = httpRequest.getHeader(X_FORWARDED_PREFERRED_USERNAME);
        CustomAuthentication authentication = userService.getAuthenticationByKeycloakId(kcUserId);
        SecurityContextHolder.getContext().setAuthentication(authentication);
        filterChain.doFilter(servletRequest, servletResponse);
    }

}
