package com.smaile.health.config;

import com.smaile.health.exception.NotFoundException;
import com.smaile.health.exception.ReferencedException;
import jakarta.validation.ConstraintViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.NativeWebRequest;
import org.zalando.problem.Problem;
import org.zalando.problem.ProblemBuilder;
import org.zalando.problem.Status;
import org.zalando.problem.spring.web.advice.ProblemHandling;
import org.zalando.problem.spring.web.advice.security.SecurityAdviceTrait;

import java.net.URI;

@ControllerAdvice
public class GlobalErrorHandlingConfig implements ProblemHandling, SecurityAdviceTrait {

    @ExceptionHandler(NotFoundException.class)
    public ResponseEntity<Problem> handleNotFoundException(
            NotFoundException exception,
            NativeWebRequest request) {
        
        Problem problem = Problem.builder()
                .withType(URI.create("https://smaile.health/problems/not-found"))
                .withTitle("Resource Not Found")
                .withStatus(Status.NOT_FOUND)
                .withDetail("The requested resource could not be found")
                .build();
                
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(problem);
    }

    @ExceptionHandler(ReferencedException.class)
    public ResponseEntity<Problem> handleReferencedException(
            ReferencedException exception,
            NativeWebRequest request) {
        
        Problem problem = Problem.builder()
                .withType(URI.create("https://smaile.health/problems/referenced-entity"))
                .withTitle("Referenced Entity")
                .withStatus(Status.CONFLICT)
                .withDetail("Cannot delete entity because it is referenced by other entities")
                .with("message", exception.getMessage())
                .build();
                
        return ResponseEntity.status(HttpStatus.CONFLICT).body(problem);
    }

    @Override
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<Problem> handleConstraintViolation(
            ConstraintViolationException exception,
            NativeWebRequest request) {
        
        ProblemBuilder problemBuilder = Problem.builder()
                .withType(URI.create("https://smaile.health/problems/constraint-violation"))
                .withTitle("Constraint Violation")
                .withStatus(Status.BAD_REQUEST);

        exception.getConstraintViolations().forEach(violation ->
                problemBuilder.with(violation.getPropertyPath().toString(), violation.getMessage())
        );

        return ResponseEntity.badRequest().body(problemBuilder.build());
    }

    @Override
    public ResponseEntity<Problem> handleMethodArgumentNotValid(
            MethodArgumentNotValidException exception,
            NativeWebRequest request) {
        
        ProblemBuilder problemBuilder = Problem.builder()
                .withType(URI.create("https://smaile.health/problems/validation-failed"))
                .withTitle("Validation Failed")
                .withStatus(Status.BAD_REQUEST);

        exception.getBindingResult().getFieldErrors().forEach(error ->
                problemBuilder.with(error.getField(), error.getDefaultMessage())
        );

        return ResponseEntity.badRequest().body(problemBuilder.build());
    }
}