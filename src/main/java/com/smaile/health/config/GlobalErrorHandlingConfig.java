package com.smaile.health.config;

import com.smaile.health.config.properties.SecurityExceptionProperties;
import com.smaile.health.exception.NotFoundException;
import com.smaile.health.exception.ReferencedException;
import com.smaile.health.security.logging.SecurityContextLogger;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.context.request.ServletWebRequest;
import org.zalando.problem.Problem;
import org.zalando.problem.ProblemBuilder;
import org.zalando.problem.Status;
import org.zalando.problem.spring.web.advice.ProblemHandling;
import org.zalando.problem.spring.web.advice.security.SecurityAdviceTrait;

import java.net.URI;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;

/**
 * Enhanced global exception handler that provides enterprise-grade error handling
 * with RFC 7807 compliance, comprehensive logging, and security considerations.
 * <p>
 * This implementation extends the existing error handling capabilities with:
 * - Enhanced security exception handling
 * - Contextual logging with security information
 * - Environment-aware error responses
 * - Prevention of information leakage
 */
@Slf4j
@ControllerAdvice
@RequiredArgsConstructor
public class GlobalErrorHandlingConfig implements ProblemHandling, SecurityAdviceTrait {

    private final SecurityExceptionProperties securityProperties;
    private final SecurityContextLogger securityLogger;
    private final Environment environment;

    /**
     * Handles security-related authentication exceptions with enhanced logging and RFC 7807 compliance.
     * Note: This handler works in conjunction with the AuthenticationEntryPoint for comprehensive coverage.
     */
    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<Problem> handleAuthenticationException(
            AuthenticationException exception,
            NativeWebRequest request) {

        HttpServletRequest httpRequest = extractHttpRequest(request);
        securityLogger.logAuthenticationFailure(httpRequest, exception);

        Problem problem = buildSecurityProblem(
                "authentication-required",
                "Authentication Required",
                Status.UNAUTHORIZED,
                "Valid authentication credentials are required to access this resource",
                httpRequest,
                exception
        );

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(problem);
    }

    /**
     * Handles access denied exceptions with enhanced logging and RFC 7807 compliance.
     * Note: This handler works in conjunction with the AccessDeniedHandler for comprehensive coverage.
     */
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<Problem> handleAccessDeniedException(
            AccessDeniedException exception,
            NativeWebRequest request) {

        HttpServletRequest httpRequest = extractHttpRequest(request);
        securityLogger.logAccessDenied(httpRequest, exception);

        Problem problem = buildSecurityProblem(
                "access-denied",
                "Access Denied",
                Status.FORBIDDEN,
                "You do not have sufficient permissions to access this resource",
                httpRequest,
                exception
        );

        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(problem);
    }

    @ExceptionHandler(NotFoundException.class)
    public ResponseEntity<Problem> handleNotFoundException(
            NotFoundException exception,
            NativeWebRequest request) {

        HttpServletRequest httpRequest = extractHttpRequest(request);

        Problem problem = buildEnhancedProblem(
                "not-found",
                "Resource Not Found",
                Status.NOT_FOUND,
                "The requested resource could not be found",
                httpRequest,
                exception
        );

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(problem);
    }

    @ExceptionHandler(ReferencedException.class)
    public ResponseEntity<Problem> handleReferencedException(
            ReferencedException exception,
            NativeWebRequest request) {

        HttpServletRequest httpRequest = extractHttpRequest(request);

        Problem problem = buildEnhancedProblem(
                "referenced-entity",
                "Referenced Entity",
                Status.CONFLICT,
                "Cannot delete entity because it is referenced by other entities",
                httpRequest,
                exception
        ).with("message", sanitizeErrorMessage(exception.getMessage()));

        return ResponseEntity.status(HttpStatus.CONFLICT).body(problem);
    }

    @Override
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<Problem> handleConstraintViolation(
            ConstraintViolationException exception,
            NativeWebRequest request) {

        HttpServletRequest httpRequest = extractHttpRequest(request);

        ProblemBuilder problemBuilder = buildEnhancedProblem(
                "constraint-violation",
                "Constraint Violation",
                Status.BAD_REQUEST,
                "One or more validation constraints were violated",
                httpRequest,
                exception
        ).toBuilder();

        exception.getConstraintViolations().forEach(violation ->
                problemBuilder.with(violation.getPropertyPath().toString(),
                        sanitizeErrorMessage(violation.getMessage()))
        );

        return ResponseEntity.badRequest().body(problemBuilder.build());
    }

    @Override
    public ResponseEntity<Problem> handleMethodArgumentNotValid(
            MethodArgumentNotValidException exception,
            NativeWebRequest request) {

        HttpServletRequest httpRequest = extractHttpRequest(request);

        ProblemBuilder problemBuilder = buildEnhancedProblem(
                "validation-failed",
                "Validation Failed",
                Status.BAD_REQUEST,
                "Request validation failed for one or more fields",
                httpRequest,
                exception
        ).toBuilder();

        exception.getBindingResult().getFieldErrors().forEach(error ->
                problemBuilder.with(error.getField(),
                        sanitizeErrorMessage(error.getDefaultMessage()))
        );

        return ResponseEntity.badRequest().body(problemBuilder.build());
    }

    /**
     * Builds an enhanced Problem object with security and enterprise features.
     */
    private Problem buildEnhancedProblem(String type, String title, Status status,
                                       String detail, HttpServletRequest request,
                                       Exception exception) {
        Problem.ProblemBuilder builder = Problem.builder()
                .withType(URI.create(securityProperties.getProblemTypeBaseUri() + "/" + type))
                .withTitle(title)
                .withStatus(status)
                .withDetail(detail)
                .withInstance(URI.create(request.getRequestURI()))
                .with("timestamp", OffsetDateTime.now().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
                .with("path", request.getRequestURI())
                .with("method", request.getMethod());

        // Add additional context in non-production environments
        if (shouldIncludeDetailedMessages()) {
            builder.with("exception", exception.getClass().getSimpleName());

            if (securityProperties.isIncludeStackTrace()) {
                builder.with("trace", getStackTraceAsString(exception));
            }
        }

        return builder.build();
    }

    /**
     * Builds a security-specific Problem object with enhanced logging and security features.
     */
    private Problem buildSecurityProblem(String type, String title, Status status,
                                       String detail, HttpServletRequest request,
                                       Exception exception) {
        return buildEnhancedProblem(type, title, status, detail, request, exception);
    }

    /**
     * Extracts HttpServletRequest from NativeWebRequest.
     */
    private HttpServletRequest extractHttpRequest(NativeWebRequest request) {
        if (request instanceof ServletWebRequest servletWebRequest) {
            return servletWebRequest.getRequest();
        }
        throw new IllegalArgumentException("Expected ServletWebRequest but got: " +
                request.getClass().getSimpleName());
    }

    /**
     * Determines whether to include detailed error messages based on environment and configuration.
     */
    private boolean shouldIncludeDetailedMessages() {
        return securityProperties.isIncludeDetailedMessages() ||
               Arrays.asList(environment.getActiveProfiles()).contains("dev") ||
               Arrays.asList(environment.getActiveProfiles()).contains("test");
    }

    /**
     * Sanitizes error messages to prevent information leakage and injection attacks.
     */
    private String sanitizeErrorMessage(String message) {
        if (message == null) {
            return "An error occurred";
        }

        // Remove potential sensitive information patterns
        String sanitized = message
                .replaceAll("(?i)(password|token|secret|key|credential)[\\s=:]*[\\w\\-\\.]+", "$1=***")
                .replaceAll("[\r\n\t]", " ")
                .trim();

        // Limit message length
        return sanitized.length() > 200 ? sanitized.substring(0, 200) + "..." : sanitized;
    }

    /**
     * Converts exception stack trace to string for debugging purposes.
     */
    private String getStackTraceAsString(Exception exception) {
        if (!securityProperties.isIncludeStackTrace()) {
            return null;
        }

        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        exception.printStackTrace(pw);
        return sw.toString();
    }
}