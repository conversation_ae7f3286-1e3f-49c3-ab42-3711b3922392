package com.smaile.health.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Configuration properties for security exception handling.
 * Provides customizable settings for error responses, logging, and security behavior.
 */
@Data
@Component
@ConfigurationProperties(prefix = "smaile.security.exceptions")
public class SecurityExceptionProperties {

    /**
     * Whether to include stack traces in error responses (should be false in production)
     */
    private boolean includeStackTrace = false;

    /**
     * Whether to include detailed error messages (should be false in production)
     */
    private boolean includeDetailedMessages = false;

    /**
     * Whether to log security exceptions
     */
    private boolean enableLogging = true;

    /**
     * Log level for security exceptions (ERROR, WARN, INFO, DEBUG)
     */
    private String logLevel = "WARN";

    /**
     * Base URI for problem type URIs
     */
    private String problemTypeBaseUri = "https://smaile.health/problems";

    /**
     * Custom headers to include in error responses
     */
    private Map<String, String> customHeaders = Map.of(
            "X-Content-Type-Options", "nosniff",
            "X-Frame-Options", "DENY",
            "X-XSS-Protection", "1; mode=block"
    );

    /**
     * Authentication error configuration
     */
    private AuthenticationErrorConfig authentication = new AuthenticationErrorConfig();

    /**
     * Access denied error configuration
     */
    private AccessDeniedErrorConfig accessDenied = new AccessDeniedErrorConfig();

    @Data
    public static class AuthenticationErrorConfig {
        private int statusCode = 401;
        private String title = "Authentication Required";
        private String detail = "Valid authentication credentials are required to access this resource";
        private String type = "authentication-required";
    }

    @Data
    public static class AccessDeniedErrorConfig {
        private int statusCode = 403;
        private String title = "Access Denied";
        private String detail = "You do not have sufficient permissions to access this resource";
        private String type = "access-denied";
    }
}
