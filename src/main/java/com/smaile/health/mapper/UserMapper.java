package com.smaile.health.mapper;

import com.smaile.health.domain.User;
import com.smaile.health.model.UserDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserMapper {

//    @Mapping(source = "organization.id", target = "organization")
//    @Mapping(target = "roles", expression = "java(user.getRoles().stream().map(role -> role.getId()).toList())")
//    UserDTO toDTO(User user);
//
//    @Mapping(target = "organization", ignore = true)
//    @Mapping(target = "roles", ignore = true)
//    User toEntity(UserDTO userDTO);
//
//    @Mapping(target = "organization", ignore = true)
//    @Mapping(target = "roles", ignore = true)
//    void updateEntityFromDTO(UserDTO userDTO, @MappingTarget User user);
}