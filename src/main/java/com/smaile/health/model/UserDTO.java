package com.smaile.health.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.UUID;

@Getter
@Setter
public class UserDTO {

    private UUID id;

    private String keycloakId;

    @NotNull
    @Size(max = 100)
    private String username;

    @NotNull
    @Size(max = 255)
    private String email;

    @NotNull
    @Size(max = 100)
    private String firstName;

    @Size(max = 100)
    private String middleName;

    @NotNull
    @Size(max = 100)
    private String lastName;

    @NotNull
    @Size(max = 20)
    private String status;

    private List<UUID> roles;

    @NotNull
    private UUID organization;

}
