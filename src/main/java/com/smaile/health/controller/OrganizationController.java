package com.smaile.health.controller;

import com.smaile.health.model.OrganizationDTO;
import com.smaile.health.service.OrganizationService;
import com.smaile.health.exception.ReferencedException;
import com.smaile.health.util.PageResponse;
import com.smaile.health.model.ReferencedWarning;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(value = "/api/organizations", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
public class OrganizationController {

    private final OrganizationService organizationService;

    @GetMapping
    public ResponseEntity<List<OrganizationDTO>> getAllOrganizations() {
        return ResponseEntity.ok(organizationService.findAll());
    }

    @GetMapping("/paged")
    public ResponseEntity<PageResponse<OrganizationDTO>> getAllOrganizationsPaged(
            @PageableDefault(size = 20, sort = "id") Pageable pageable) {
        return ResponseEntity.ok(PageResponse.of(organizationService.findAll(pageable)));
    }

    @GetMapping("/{id}")
    public ResponseEntity<OrganizationDTO> getOrganization(
            @PathVariable(name = "id") final UUID id) {
        return ResponseEntity.ok(organizationService.get(id));
    }

    @PostMapping
    @ApiResponse(responseCode = "201")
    @PreAuthorize("hasRole('ADMIN') or hasRole('ORGANIZATION_MANAGER')")
    public ResponseEntity<String> createOrganization(
            @RequestBody @Valid final OrganizationDTO organizationDTO) {
        final UUID createdId = organizationService.create(organizationDTO);
        return new ResponseEntity<>(createdId.toString(), HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('ORGANIZATION_MANAGER')")
    public ResponseEntity<String> updateOrganization(@PathVariable(name = "id") final UUID id,
                                                   @RequestBody @Valid final OrganizationDTO organizationDTO) {
        organizationService.update(id, organizationDTO);
        return ResponseEntity.ok(id.toString());
    }

    @DeleteMapping("/{id}")
    @ApiResponse(responseCode = "204")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteOrganization(@PathVariable(name = "id") final UUID id) {
        final ReferencedWarning referencedWarning = organizationService.getReferencedWarning(id);
        if (referencedWarning != null) {
            throw new ReferencedException(referencedWarning);
        }
        organizationService.delete(id);
        return ResponseEntity.noContent().build();
    }

}
