package com.smaile.health.security.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.smaile.health.config.properties.SecurityExceptionProperties;
import com.smaile.health.security.UserSecurityContext;
import com.smaile.health.security.logging.SecurityContextLogger;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;
import org.zalando.problem.Problem;
import org.zalando.problem.Status;

import java.io.IOException;
import java.net.URI;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * Enterprise-grade AccessDeniedHandler implementation that provides
 * RFC 7807 compliant error responses with comprehensive logging and security features.
 * 
 * This implementation follows SOLID principles:
 * - Single Responsibility: Handles only access denied concerns
 * - Open/Closed: Extensible through configuration properties
 * - Liskov Substitution: Properly implements AccessDeniedHandler contract
 * - Interface Segregation: Uses focused interfaces
 * - Dependency Inversion: Depends on abstractions (interfaces/properties)
 */
@Component
@RequiredArgsConstructor
public class EnterpriseAccessDeniedHandler implements AccessDeniedHandler {

    private final SecurityExceptionProperties properties;
    private final SecurityContextLogger securityLogger;
    private final ObjectMapper objectMapper;

    /**
     * Handles access denied exceptions by generating RFC 7807 compliant error responses
     * and logging security events.
     *
     * @param request the HTTP request
     * @param response the HTTP response
     * @param accessDeniedException the access denied exception
     * @throws IOException if an I/O error occurs
     */
    @Override
    public void handle(HttpServletRequest request,
                      HttpServletResponse response,
                      AccessDeniedException accessDeniedException) throws IOException {

        // Log the access denied event
        securityLogger.logAccessDenied(request, accessDeniedException);

        // Build RFC 7807 compliant problem response
        Problem problem = buildAccessDeniedProblem(request, accessDeniedException);

        // Configure response
        configureResponse(response);

        // Write response body
        response.getWriter().write(objectMapper.writeValueAsString(problem));
        response.getWriter().flush();
    }

    /**
     * Builds an RFC 7807 compliant Problem object for access denied errors.
     *
     * @param request the HTTP request
     * @param accessDeniedException the access denied exception
     * @return Problem object
     */
    private Problem buildAccessDeniedProblem(HttpServletRequest request,
                                           AccessDeniedException accessDeniedException) {
        SecurityExceptionProperties.AccessDeniedErrorConfig config = properties.getAccessDenied();
        
        Problem.ProblemBuilder builder = Problem.builder()
                .withType(URI.create(properties.getProblemTypeBaseUri() + "/" + config.getType()))
                .withTitle(config.getTitle())
                .withStatus(Status.valueOf(config.getStatusCode()))
                .withDetail(determineErrorDetail(accessDeniedException, config.getDetail()))
                .withInstance(URI.create(request.getRequestURI()))
                .with("timestamp", OffsetDateTime.now().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
                .with("path", request.getRequestURI())
                .with("method", request.getMethod());

        // Add user context if available and detailed messages are enabled
        if (properties.isIncludeDetailedMessages()) {
            addUserContext(builder);
            builder.with("exception", accessDeniedException.getClass().getSimpleName());
            
            if (properties.isIncludeStackTrace()) {
                builder.with("trace", getStackTraceAsString(accessDeniedException));
            }
        }

        return builder.build();
    }

    /**
     * Adds user context information to the problem response if available.
     *
     * @param builder the problem builder
     */
    private void addUserContext(Problem.ProblemBuilder builder) {
        getCurrentUserContext().ifPresent(userContext -> {
            builder.with("userId", userContext.getUserId())
                   .with("userRole", userContext.getPrimaryRole())
                   .with("permissions", userContext.getPermissions().size());
        });
    }

    /**
     * Retrieves the current user context from the security context.
     *
     * @return optional user security context
     */
    private Optional<UserSecurityContext> getCurrentUserContext() {
        return Optional.ofNullable(SecurityContextHolder.getContext().getAuthentication())
                .filter(Authentication::isAuthenticated)
                .map(Authentication::getDetails)
                .filter(UserSecurityContext.class::isInstance)
                .map(UserSecurityContext.class::cast);
    }

    /**
     * Configures the HTTP response with appropriate headers and status.
     *
     * @param response the HTTP response
     */
    private void configureResponse(HttpServletResponse response) {
        SecurityExceptionProperties.AccessDeniedErrorConfig config = properties.getAccessDenied();
        
        response.setStatus(config.getStatusCode());
        response.setContentType(MediaType.APPLICATION_PROBLEM_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");

        // Add security headers
        properties.getCustomHeaders().forEach(response::setHeader);

        // Add cache control headers to prevent caching of error responses
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
    }

    /**
     * Determines the appropriate error detail message based on configuration and exception.
     *
     * @param accessDeniedException the access denied exception
     * @param defaultDetail the default detail message
     * @return appropriate error detail
     */
    private String determineErrorDetail(AccessDeniedException accessDeniedException, String defaultDetail) {
        if (!properties.isIncludeDetailedMessages()) {
            return defaultDetail;
        }

        // In development/test environments, provide more specific error messages
        String exceptionMessage = accessDeniedException.getMessage();
        if (exceptionMessage != null && !exceptionMessage.trim().isEmpty()) {
            return sanitizeErrorMessage(exceptionMessage);
        }

        return defaultDetail;
    }

    /**
     * Sanitizes error messages to prevent information leakage and injection attacks.
     *
     * @param message the original error message
     * @return sanitized error message
     */
    private String sanitizeErrorMessage(String message) {
        if (message == null) {
            return "Access denied";
        }

        // Remove potential sensitive information patterns
        String sanitized = message
                .replaceAll("(?i)(password|token|secret|key|credential)[\\s=:]*[\\w\\-\\.]+", "$1=***")
                .replaceAll("[\r\n\t]", " ")
                .trim();

        // Limit message length
        return sanitized.length() > 200 ? sanitized.substring(0, 200) + "..." : sanitized;
    }

    /**
     * Converts exception stack trace to string for debugging purposes.
     *
     * @param exception the exception
     * @return stack trace as string
     */
    private String getStackTraceAsString(Exception exception) {
        if (!properties.isIncludeStackTrace()) {
            return null;
        }

        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        exception.printStackTrace(pw);
        return sw.toString();
    }
}
