package com.smaile.health.security.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.smaile.health.config.properties.SecurityExceptionProperties;
import com.smaile.health.security.logging.SecurityContextLogger;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;
import org.zalando.problem.Problem;
import org.zalando.problem.Status;

import java.io.IOException;
import java.net.URI;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;

/**
 * AuthenticationEntryPoint implementation that provides
 * error responses with comprehensive logging and security features.
 */
@Component
@RequiredArgsConstructor
public class SmaileAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private final SecurityExceptionProperties properties;
    private final SecurityContextLogger securityLogger;
    private final ObjectMapper objectMapper;

    /**
     * Handles authentication failures by generating RFC 7807 compliant error responses
     * and logging security events.
     *
     * @param request       the HTTP request
     * @param response      the HTTP response
     * @param authException the authentication exception
     * @throws IOException if an I/O error occurs
     */
    @Override
    public void commence(HttpServletRequest request,
                         HttpServletResponse response,
                         AuthenticationException authException) throws IOException {
        securityLogger.logAuthenticationFailure(request, authException);
        Problem problem = buildAuthenticationProblem(request, authException);
        configureResponse(response);

        response.getWriter().write(objectMapper.writeValueAsString(problem));
        response.getWriter().flush();
    }

    /**
     * Builds an RFC 7807 compliant Problem object for authentication failures.
     *
     * @param request       the HTTP request
     * @param authException the authentication exception
     * @return Problem object
     */
    private Problem buildAuthenticationProblem(HttpServletRequest request, AuthenticationException authException) {
        SecurityExceptionProperties.AuthenticationErrorConfig config = properties.getAuthentication();

        Problem.ProblemBuilder builder = Problem.builder()
                .withType(URI.create(properties.getProblemTypeBaseUri() + "/" + config.getType()))
                .withTitle(config.getTitle()).withStatus(Status.valueOf(config.getStatusCode()))
                .withDetail(determineErrorDetail(authException, config.getDetail()))
                .withInstance(URI.create(request.getRequestURI()))
                .with("timestamp", OffsetDateTime.now().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
                .with("path", request.getRequestURI()).with("method", request.getMethod());

        // Add additional context in non-production environments
        if (properties.isIncludeDetailedMessages()) {
            builder.with("exception", authException.getClass().getSimpleName());

            if (properties.isIncludeStackTrace()) {
                builder.with("trace", getStackTraceAsString(authException));
            }
        }

        return builder.build();
    }

    /**
     * Configures the HTTP response with appropriate headers and status.
     *
     * @param response the HTTP response
     */
    private void configureResponse(HttpServletResponse response) {
        SecurityExceptionProperties.AuthenticationErrorConfig config = properties.getAuthentication();

        response.setStatus(config.getStatusCode());
        response.setContentType(MediaType.APPLICATION_PROBLEM_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");

        // Add security headers
        properties.getCustomHeaders().forEach(response::setHeader);

        // Add cache control headers to prevent caching of error responses
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
    }

    /**
     * Determines the appropriate error detail message based on configuration and exception.
     *
     * @param authException the authentication exception
     * @param defaultDetail the default detail message
     * @return appropriate error detail
     */
    private String determineErrorDetail(AuthenticationException authException, String defaultDetail) {
        if (!properties.isIncludeDetailedMessages()) {
            return defaultDetail;
        }

        // In development/test environments, provide more specific error messages
        String exceptionMessage = authException.getMessage();
        if (exceptionMessage != null && !exceptionMessage.trim().isEmpty()) {
            return sanitizeErrorMessage(exceptionMessage);
        }

        return defaultDetail;
    }

    /**
     * Sanitizes error messages to prevent information leakage and injection attacks.
     *
     * @param message the original error message
     * @return sanitized error message
     */
    private String sanitizeErrorMessage(String message) {
        if (message == null) {
            return "Authentication failed";
        }

        // Remove potential sensitive information patterns
        String sanitized = message.replaceAll("(?i)(password|token|secret|key|credential)[\\s=:]*[\\w\\-\\.]+",
                "$1=***").replaceAll("[\r\n\t]", " ").trim();

        // Limit message length
        return sanitized.length() > 200 ? sanitized.substring(0, 200) + "..." : sanitized;
    }

    /**
     * Converts exception stack trace to string for debugging purposes.
     *
     * @param exception the exception
     * @return stack trace as string
     */
    private String getStackTraceAsString(Exception exception) {
        if (!properties.isIncludeStackTrace()) {
            return null;
        }

        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        exception.printStackTrace(pw);
        return sw.toString();
    }

}
