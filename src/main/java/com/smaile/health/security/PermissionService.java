package com.smaile.health.security;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

@Service("permissionService")
@RequiredArgsConstructor
@Slf4j
public class PermissionService {
    
    public boolean canPerform(Authentication authentication, String action) {
        return canPerform(authentication, action, null);
    }
    
    public boolean canPerform(Authentication authentication, String action, Long resourceId) {
        if (!(authentication instanceof SmaileAuthentication smaileAuth)) {
            log.warn("Invalid authentication type: {}", authentication.getClass());
            return false;
        }
        
        UserSecurityContext context = smaileAuth.getUserContext();
        
        if (context.isSuperAdmin()) {
            log.debug("SUPER_ADMIN access granted for action: {}", action);
            return true;
        }
        
        if (!context.isUserActive()) {
            log.debug("User inactive, denying access to action: {}", action);
            return false;
        }
        
        if (context.isReadOnlyMode() && !isReadOnlyAction(action)) {
            log.debug("Read-only mode, denying write action: {}", action);
            return false;
        }
        
        boolean hasPermission = context.hasPermission(action);
        
        if (hasPermission && resourceId != null) {
            hasPermission = canAccessResource(context, action, resourceId);
        }
        
        log.debug("Permission check - User: {}, Action: {}, Resource: {}, Result: {}", 
                 context.getEmail(), action, resourceId, hasPermission);
        
        return hasPermission;
    }
    
    public boolean canAccessOrganization(Authentication authentication, Long organizationId) {
        if (!(authentication instanceof SmaileAuthentication platformAuth)) {
            return false;
        }
        
        UserSecurityContext context = platformAuth.getUserContext();
        
        if (context.isSuperAdmin()) {
            return true;
        }
        
        return context.canAccessOrganization(organizationId);
    }
    
    public boolean canManageUser(Authentication authentication, Long targetUserId) {
        return canPerform(authentication, "USER_UPDATE") &&
               hasUserManagementScope(authentication, targetUserId);
    }
    
    public boolean canCreateChildOrganization(Authentication authentication, String organizationType) {
        if (!canPerform(authentication, "ORGANIZATION_CREATE")) {
            return false;
        }
        
        SmaileAuthentication platformAuth = (SmaileAuthentication) authentication;
        UserSecurityContext context = platformAuth.getUserContext();
        
        return isValidParentChildRelationship(context.getPrimaryRole(), organizationType);
    }
    
    private boolean canAccessResource(UserSecurityContext context, String action, Long resourceId) {
        String resourceType = action.split("_")[0];
        
        return switch (resourceType) {
            case "ORGANIZATION" -> context.canAccessOrganization(resourceId);
            case "USER" -> canAccessUser(context, resourceId);
            case "CASE" -> canAccessCase(context, resourceId);
            default -> true; // Allow access for unknown resource types
        };
    }
    
    private boolean canAccessUser(UserSecurityContext context, Long userId) {
        // User can access other users in their accessible organizations
        // Implementation would check if target user belongs to accessible org
        return true; // Simplified for now
    }
    
    private boolean canAccessCase(UserSecurityContext context, Long caseId) {
        // Case access based on organization membership and contracts
        // Implementation would check case organization and contract status
        return true; // Simplified for now
    }
    
    private boolean hasUserManagementScope(Authentication authentication, Long targetUserId) {
        // Check if current user has authority to manage target user
        // Based on organizational hierarchy and roles
        return true; // Simplified for now
    }
    
    private boolean isValidParentChildRelationship(String parentRole, String childOrgType) {
        return switch (parentRole) {
            case "SUPER_ADMIN" -> true; // Can create any type
            case "IC_ADMIN" -> "THIRD_PARTY_ADMIN".equals(childOrgType) || "PROVIDER".equals(childOrgType);
            case "TPA_ADMIN" -> "PROVIDER".equals(childOrgType);
            default -> false;
        };
    }
    
    private boolean isReadOnlyAction(String action) {
        return action.endsWith("_READ") || 
               action.endsWith("_EXPORT") || 
               action.startsWith("VIEW_") ||
               action.startsWith("GET_");
    }
    
    // Helper method to get current user context
    public UserSecurityContext getCurrentUserContext() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth instanceof SmaileAuthentication platformAuth) {
            return platformAuth.getUserContext();
        }
        throw new SecurityException("No valid security context found");
    }
}