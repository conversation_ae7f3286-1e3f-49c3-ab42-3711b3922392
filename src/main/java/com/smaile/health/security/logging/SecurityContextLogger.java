package com.smaile.health.security.logging;

import com.smaile.health.config.properties.SecurityExceptionProperties;
import com.smaile.health.security.UserSecurityContext;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * Service responsible for logging security-related events with contextual information.
 * Implements enterprise logging standards while preventing information leakage.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SecurityContextLogger {

    private final SecurityExceptionProperties properties;

    /**
     * Logs authentication failure events with contextual information.
     *
     * @param request the HTTP request
     * @param exception the authentication exception
     */
    public void logAuthenticationFailure(HttpServletRequest request, Exception exception) {
        if (!properties.isEnableLogging()) {
            return;
        }

        SecurityLogContext context = buildSecurityLogContext(request);
        String message = "Authentication failed for request to {} from IP {} at {}";
        
        logWithLevel(properties.getLogLevel(), message, 
                context.getRequestUri(), 
                context.getClientIp(), 
                context.getTimestamp(),
                exception);
    }

    /**
     * Logs access denied events with contextual information.
     *
     * @param request the HTTP request
     * @param exception the access denied exception
     */
    public void logAccessDenied(HttpServletRequest request, Exception exception) {
        if (!properties.isEnableLogging()) {
            return;
        }

        SecurityLogContext context = buildSecurityLogContext(request);
        String userId = getCurrentUserId().orElse("anonymous");
        
        String message = "Access denied for user {} to {} from IP {} at {}";
        
        logWithLevel(properties.getLogLevel(), message,
                userId,
                context.getRequestUri(),
                context.getClientIp(),
                context.getTimestamp(),
                exception);
    }

    /**
     * Logs general security events with contextual information.
     *
     * @param request the HTTP request
     * @param event the security event description
     * @param level the log level
     */
    public void logSecurityEvent(HttpServletRequest request, String event, String level) {
        if (!properties.isEnableLogging()) {
            return;
        }

        SecurityLogContext context = buildSecurityLogContext(request);
        String userId = getCurrentUserId().orElse("anonymous");
        
        String message = "Security event: {} - User: {}, URI: {}, IP: {}, Time: {}";
        
        logWithLevel(level, message,
                event,
                userId,
                context.getRequestUri(),
                context.getClientIp(),
                context.getTimestamp());
    }

    /**
     * Builds security log context from the HTTP request.
     *
     * @param request the HTTP request
     * @return security log context
     */
    private SecurityLogContext buildSecurityLogContext(HttpServletRequest request) {
        return SecurityLogContext.builder()
                .requestUri(sanitizeUri(request.getRequestURI()))
                .method(request.getMethod())
                .clientIp(extractClientIp(request))
                .userAgent(sanitizeUserAgent(request.getHeader("User-Agent")))
                .timestamp(OffsetDateTime.now().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
                .sessionId(sanitizeSessionId(request.getSession(false) != null ? 
                        request.getSession().getId() : null))
                .build();
    }

    /**
     * Extracts the current user ID from the security context.
     *
     * @return optional user ID
     */
    private Optional<String> getCurrentUserId() {
        return Optional.ofNullable(SecurityContextHolder.getContext().getAuthentication())
                .filter(Authentication::isAuthenticated)
                .map(Authentication::getDetails)
                .filter(UserSecurityContext.class::isInstance)
                .map(UserSecurityContext.class::cast)
                .map(UserSecurityContext::getUserId);
    }

    /**
     * Extracts client IP address from the request, considering proxy headers.
     *
     * @param request the HTTP request
     * @return client IP address
     */
    private String extractClientIp(HttpServletRequest request) {
        String[] headerNames = {
                "X-Forwarded-For",
                "X-Real-IP",
                "X-Originating-IP",
                "CF-Connecting-IP"
        };

        for (String headerName : headerNames) {
            String ip = request.getHeader(headerName);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                // Take the first IP in case of comma-separated list
                return ip.split(",")[0].trim();
            }
        }

        return request.getRemoteAddr();
    }

    /**
     * Sanitizes URI to prevent log injection attacks.
     *
     * @param uri the original URI
     * @return sanitized URI
     */
    private String sanitizeUri(String uri) {
        if (uri == null) {
            return "unknown";
        }
        // Remove potential log injection characters
        return uri.replaceAll("[\r\n\t]", "_").substring(0, Math.min(uri.length(), 200));
    }

    /**
     * Sanitizes user agent to prevent log injection attacks.
     *
     * @param userAgent the original user agent
     * @return sanitized user agent
     */
    private String sanitizeUserAgent(String userAgent) {
        if (userAgent == null) {
            return "unknown";
        }
        // Remove potential log injection characters and limit length
        return userAgent.replaceAll("[\r\n\t]", "_").substring(0, Math.min(userAgent.length(), 100));
    }

    /**
     * Sanitizes session ID for logging.
     *
     * @param sessionId the original session ID
     * @return sanitized session ID
     */
    private String sanitizeSessionId(String sessionId) {
        if (sessionId == null) {
            return "none";
        }
        // Only log first 8 characters for privacy
        return sessionId.length() > 8 ? sessionId.substring(0, 8) + "..." : sessionId;
    }

    /**
     * Logs message with the specified level.
     *
     * @param level the log level
     * @param message the message template
     * @param args the message arguments
     */
    private void logWithLevel(String level, String message, Object... args) {
        switch (level.toUpperCase()) {
            case "ERROR" -> log.error(message, args);
            case "WARN" -> log.warn(message, args);
            case "INFO" -> log.info(message, args);
            case "DEBUG" -> log.debug(message, args);
            default -> log.warn(message, args);
        }
    }
}
