package com.smaile.health.security.logging;

import lombok.Builder;
import lombok.Data;

/**
 * Context object for security logging that contains relevant information
 * about the request and security event.
 */
@Data
@Builder
public class SecurityLogContext {
    
    /**
     * The request URI (sanitized)
     */
    private String requestUri;
    
    /**
     * HTTP method
     */
    private String method;
    
    /**
     * Client IP address
     */
    private String clientIp;
    
    /**
     * User agent (sanitized)
     */
    private String userAgent;
    
    /**
     * Timestamp of the event
     */
    private String timestamp;
    
    /**
     * Session ID (partially masked for privacy)
     */
    private String sessionId;
}
