package com.smaile.health.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Component
@Order(1)
@RequiredArgsConstructor
@Slf4j
public class SecurityContextFilter extends OncePerRequestFilter {

    private final SecurityContextService securityContextService;

    private static final String USER_ID_HEADER = "X-User-ID";
    private static final String USER_EMAIL_HEADER = "X-User-Email";

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain filterChain) throws ServletException, IOException {

        try {
            String userProviderId = request.getHeader(USER_ID_HEADER);
            String userEmail = request.getHeader(USER_EMAIL_HEADER);

            log.debug("Processing request with headers - User-ID: {}, Email: {}", userProviderId, userEmail);

            if (userProviderId != null && userEmail != null) {
                try {
                    // Build security context from headers
                    UserSecurityContext userContext = securityContextService.buildSecurityContext(userProviderId);

                    // Validate email matches
                    if (!userEmail.equals(userContext.getEmail())) {
                        log.warn("Email mismatch - Header: {}, DB: {}", userEmail, userContext.getEmail());
                        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                        return;
                    }

                    // Create Spring Security authentication
                    SmaileAuthentication authentication = new SmaileAuthentication(userContext);
                    SecurityContextHolder.getContext().setAuthentication(authentication);

                    log.debug("Security context set for user: {}, roles: {}",
                            userContext.getEmail(), userContext.getAllRoles());

                } catch (Exception e) {
                    log.error("Failed to build security context for user: {}", userProviderId, e);
                    response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                    return;
                }
            } else if (isProtectedEndpoint(request)) {
                log.debug("Missing required headers for protected endpoint: {}", request.getRequestURI());
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return;
            }

            filterChain.doFilter(request, response);

        } finally {
            // Clear security context
            SecurityContextHolder.clearContext();
        }
    }

    private boolean isProtectedEndpoint(HttpServletRequest request) {
        String uri = request.getRequestURI();
        return !uri.startsWith("/api/public/") &&
                !uri.startsWith("/actuator/health") &&
                !uri.startsWith("/swagger-ui/") &&
                !uri.startsWith("/api-docs/");
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        String path = request.getRequestURI();
        return path.startsWith("/api/public/") ||
                path.startsWith("/actuator/health") ||
                path.startsWith("/swagger-ui/") ||
                path.startsWith("/api-docs/");
    }

}