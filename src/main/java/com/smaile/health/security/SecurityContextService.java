package com.smaile.health.security;

import com.smaile.health.domain.Permission;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
@Slf4j
public class SecurityContextService {
    
    private final UserRepository userRepository;
    private final UserOrganizationRoleRepository userOrganizationRoleRepository;
    private final OrganizationRepository organizationRepository;
    private final RolePermissionRepository rolePermissionRepository;
    
    public UserSecurityContext buildSecurityContext(String keycloakUserId) {
        log.debug("Building security context for keycloak user: {}", keycloakUserId);
        
        User user = userRepository.findByKeycloakUserId(keycloakUserId)
            .orElseThrow(() -> new SecurityException("User not found: " + keycloakUserId));
        
        LocalDate currentDate = LocalDate.now();
        
        // Check user lifecycle status
        boolean isUserActive = user.isActive();
        boolean isOwnOrgActive = user.getOwnOrganization().isActive();
        
        // Get active roles for user
        List<UserOrganizationRole> activeRoles = userOrganizationRoleRepository
            .findActiveRolesByUserId(user.getId(), currentDate);
        
        UserOrganizationRole primaryRole = userOrganizationRoleRepository
            .findPrimaryRoleByUserId(user.getId(), currentDate)
            .orElse(null);
        
        // Build role map
        Map<Long, String> allRoles = activeRoles.stream()
            .collect(Collectors.toMap(
                role -> role.getOrganization().getId(),
                role -> role.getRole().getName(),
                (existing, replacement) -> existing // Keep first if duplicate
            ));
        
        // Calculate accessible organizations
        Set<Long> accessibleOrgIds = calculateAccessibleOrganizations(activeRoles, currentDate);
        
        // Calculate permissions
        Set<String> permissions = calculatePermissions(activeRoles, isUserActive, isOwnOrgActive);
        
        // Determine read-only mode
        boolean isReadOnlyMode = determineReadOnlyMode(user, isUserActive, isOwnOrgActive, activeRoles);
        
        return UserSecurityContext.builder()
            .userId(user.getId())
            .email(user.getEmail())
            .firstName(user.getFirstName())
            .lastName(user.getLastName())
            .ownOrganizationId(user.getOwnOrganization().getId())
            .primaryRole(primaryRole != null ? primaryRole.getRole().getName() : null)
            .allRoles(allRoles)
            .accessibleOrganizationIds(accessibleOrgIds)
            .permissions(permissions)
            .isUserActive(isUserActive)
            .isOwnOrganizationActive(isOwnOrgActive)
            .isReadOnlyMode(isReadOnlyMode)
            .contextBuiltAt(LocalDateTime.now())
            .build();
    }
    
    private Set<Long> calculateAccessibleOrganizations(List<UserOrganizationRole> activeRoles, 
                                                      LocalDate currentDate) {
        Set<Long> accessible = new HashSet<>();
        
        for (UserOrganizationRole role : activeRoles) {
            Long orgId = role.getOrganization().getId();
            accessible.add(orgId);

            // Add child organizations based on role permissions
            if (hasChildOrgAccess(role)) {
                Set<Long> childIds = organizationRepository.findActiveChildIds(orgId, currentDate);
                accessible.addAll(childIds);
            }
        }

        return accessible;
    }

    private Set<String> calculatePermissions(List<UserOrganizationRole> activeRoles,
                                             boolean isUserActive,
                                             boolean isOwnOrgActive) {
        Set<String> permissions = new HashSet<>();

        for (UserOrganizationRole userRole : activeRoles) {
            List<RolePermission> rolePermissions = rolePermissionRepository
                    .findByRoleId(userRole.getRole().getId());

            for (RolePermission rp : rolePermissions) {
                String permissionKey = rp.getPermission().getResourceType() + "_" +
                        rp.getPermission().getAction();

                // Apply lifecycle restrictions
                if (shouldIncludePermission(rp.getPermission(), isUserActive, isOwnOrgActive, userRole)) {
                    permissions.add(permissionKey);
                }
            }
        }

        return permissions;
    }

    private boolean shouldIncludePermission(Permission permission,
                                            boolean isUserActive,
                                            boolean isOwnOrgActive,
                                            UserOrganizationRole userRole) {
        // SUPER_ADMIN always gets all permissions
        if ("SUPER_ADMIN".equals(userRole.getRole().getName())) {
            return true;
        }

        // If user is inactive, no permissions
        if (!isUserActive) {
            return false;
        }

        // If organization is inactive, only read-only permissions
        if (!isOwnOrgActive) {
            return isReadOnlyPermission(permission);
        }

        return true;
    }

    private boolean isReadOnlyPermission(Permission permission) {
        return permission.getAction() == Action.READ ||
                permission.getAction() == Action.EXPORT;
    }

    private boolean hasChildOrgAccess(UserOrganizationRole role) {
        List<RolePermission> permissions = rolePermissionRepository.findByRoleId(role.getRole().getId());

        return permissions.stream()
                .anyMatch(rp -> rp.getScope() == PermissionScope.CHILD_ORGS ||
                        rp.getScope() == PermissionScope.ALL);
    }

    private boolean determineReadOnlyMode(User user,
                                          boolean isUserActive,
                                          boolean isOwnOrgActive,
                                          List<UserOrganizationRole> activeRoles) {
        // SUPER_ADMIN never in read-only mode
        boolean isSuperAdmin = activeRoles.stream()
                .anyMatch(role -> "SUPER_ADMIN".equals(role.getRole().getName()));

        if (isSuperAdmin) {
            return false;
        }

        // Read-only if user inactive or organization inactive
        return !isUserActive || !isOwnOrgActive;
    }
}