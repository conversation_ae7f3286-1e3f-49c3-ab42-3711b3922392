package com.smaile.health.security;

import lombok.Getter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
public class SmaileAuthentication extends AbstractAuthenticationToken {

    private final UserSecurityContext userContext;

    public SmaileAuthentication(UserSecurityContext userContext) {
        super(createAuthorities(userContext));
        this.userContext = userContext;
        setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        return null;
    }

    @Override
    public Object getDetails() {
        return userContext;
    }

    @Override
    public Object getPrincipal() {
        return userContext.getUserId();
    }

    private static Collection<? extends GrantedAuthority> createAuthorities(UserSecurityContext context) {
        Set<GrantedAuthority> authorities = context.getPermissions().stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toSet());

        authorities.add(new SimpleGrantedAuthority("ROLE_" + context.getPrimaryRole()));

        return authorities;
    }

}