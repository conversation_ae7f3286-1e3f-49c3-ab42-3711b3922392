package com.smaile.health.security;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;

@Data
@Builder
public class UserSecurityContext {
    private Long userId;
    private String email;
    private String firstName;
    private String lastName;
    
    private Long ownOrganizationId;
    private String primaryRole;
    private Map<Long, String> allRoles;
    
    private Set<Long> accessibleOrganizationIds;
    private Set<String> permissions;
    
    private boolean isUserActive;
    private boolean isOwnOrganizationActive;
    private boolean isReadOnlyMode;
    
    private LocalDateTime contextBuiltAt;
    
    public boolean hasRole(String roleName) {
        return allRoles.containsValue(roleName);
    }
    
    public boolean hasRoleInOrganization(Long organizationId, String roleName) {
        return roleName.equals(allRoles.get(organizationId));
    }
    
    public boolean hasPermission(String permission) {
        return permissions.contains(permission);
    }
    
    public boolean canAccessOrganization(Long organizationId) {
        return accessibleOrganizationIds.contains(organizationId);
    }
    
    public boolean isSuperAdmin() {
        return hasRole("SUPER_ADMIN");
    }
}