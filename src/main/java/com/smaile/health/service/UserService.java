package com.smaile.health.service;

import com.smaile.health.config.CustomAuthentication;
import com.smaile.health.model.UserDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

public interface UserService {

    CustomAuthentication getAuthenticationByKeycloakId(String keycloakId);

    List<UserDTO> findAll();

    Page<UserDTO> findAll(Pageable pageable);

    UserDTO get(UUID id);

    UUID create(UserDTO userDTO);

    void update(UUID id, UserDTO userDTO);

    void delete(UUID id);

    boolean usernameExists(String username);

    boolean emailExists(String email);
}