package com.smaile.health.service;

import com.smaile.health.model.OrganizationDTO;
import com.smaile.health.model.ReferencedWarning;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

public interface OrganizationService {

    List<OrganizationDTO> findAll();

    Page<OrganizationDTO> findAll(Pageable pageable);

    OrganizationDTO get(UUID id);

    UUID create(OrganizationDTO organizationDTO);

    void update(UUID id, OrganizationDTO organizationDTO);

    void delete(UUID id);

    boolean nameExists(String name);

    boolean codeExists(String code);

    ReferencedWarning getReferencedWarning(UUID id);
}