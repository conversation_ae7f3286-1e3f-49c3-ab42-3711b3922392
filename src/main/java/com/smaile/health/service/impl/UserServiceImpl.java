package com.smaile.health.service.impl;

import com.smaile.health.config.CustomAuthentication;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.Role;
import com.smaile.health.domain.User;
import com.smaile.health.domain.UserRoles;
import com.smaile.health.model.UserDTO;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.repository.RoleRepository;
import com.smaile.health.repository.UserRepository;
import com.smaile.health.exception.NotFoundException;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.UUID;
import java.util.Set;

import com.smaile.health.service.UserService;
import com.smaile.health.mapper.UserMapper;

@Service
@Transactional(rollbackFor = Exception.class)
@AllArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final OrganizationRepository organizationRepository;
    private final UserMapper userMapper;

    @Override
    public CustomAuthentication getAuthenticationByKeycloakId(String keycloakId) {
        CustomAuthentication authentication = new CustomAuthentication();
        try {
            User user = userRepository.findOneByKeycloakId(keycloakId);
            if (user == null) {
                authentication.setAuthenticated(false);
                return authentication;
            }
            List<String> roles = user.getUserOrganizations().stream()
                    .flatMap(org -> org.getUserRoles().stream())
                    .map(UserRoles::getRole)
                    .map(Role::getName)
                    .toList();
            authentication.setKcUserId(user.getKeycloakId());
            authentication.setUserId(String.valueOf(user.getId()));
            authentication.setRoles(roles);
            authentication.setEmail(user.getEmail());
            authentication.setPreferredUsername(user.getUsername());
            authentication.setAuthenticated(true);
        } catch (Exception e) {
            authentication.setAuthenticated(false);
        }
        return authentication;
    }

    public List<UserDTO> findAll() {
        final List<User> users = userRepository.findAll(Sort.by("id"));
        return null; //users.stream()
//                .map(userMapper::toDTO)
//                .toList();
    }

    public Page<UserDTO> findAll(Pageable pageable) {
        final Page<User> users = userRepository.findAll(pageable);
        return null; //users.map(userMapper::toDTO);
    }

    public UserDTO get(final UUID id) {
        return null;
//        userRepository.findById(id)
//                .map(userMapper::toDTO)
//                .orElseThrow(NotFoundException::new);
    }

    public UUID create(final UserDTO userDTO) {
//        final User user = userMapper.toEntity(userDTO);
//        setUserRelations(userDTO, user);
        return null; //userRepository.save(user).getId();
    }

    public void update(final UUID id, final UserDTO userDTO) {
//        final User user = userRepository.findById(id)
//                .orElseThrow(NotFoundException::new);
//        userMapper.updateEntityFromDTO(userDTO, user);
//        setUserRelations(userDTO, user);
//        userRepository.save(user);
    }

    public void delete(final UUID id) {
        userRepository.deleteById(id);
    }

    private void setUserRelations(final UserDTO userDTO, final User user) {
        final List<Role> roles = roleRepository.findAllById(
                userDTO.getRoles() == null ? List.of() : userDTO.getRoles());
        if (roles.size() != (userDTO.getRoles() == null ? 0 : userDTO.getRoles().size())) {
            throw new NotFoundException("one of roles not found");
        }
//        user.setRoles(new HashSet<>(roles));
//        final Organization organization = userDTO.getOrganization() == null ?
//                null :
//                organizationRepository.findById(userDTO.getOrganization())
//                        .orElseThrow(() -> new NotFoundException("organization not found"));
//        assert organization != null;
//        user.setOrganizations(Set.of(organization));
    }

    public boolean usernameExists(final String username) {
        return userRepository.existsByUsernameIgnoreCase(username);
    }

    public boolean emailExists(final String email) {
        return userRepository.existsByEmailIgnoreCase(email);
    }

}
