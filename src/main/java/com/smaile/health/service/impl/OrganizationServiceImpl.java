package com.smaile.health.service.impl;

import com.smaile.health.mapper.OrganizationMapper;
import com.smaile.health.service.OrganizationService;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.User;
import com.smaile.health.exception.NotFoundException;
import com.smaile.health.model.OrganizationDTO;
import com.smaile.health.model.ReferencedWarning;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.repository.UserRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@Transactional(rollbackFor = Exception.class)
public class OrganizationServiceImpl implements OrganizationService {

    private final OrganizationRepository organizationRepository;
    private final UserRepository userRepository;
    private final OrganizationMapper organizationMapper;

    public OrganizationServiceImpl(final OrganizationRepository organizationRepository,
                                   final UserRepository userRepository,
                                   final OrganizationMapper organizationMapper) {
        this.organizationRepository = organizationRepository;
        this.userRepository = userRepository;
        this.organizationMapper = organizationMapper;
    }

    public List<OrganizationDTO> findAll() {
        final List<Organization> organizations = organizationRepository.findAll(Sort.by("id"));
        return organizations.stream()
                .map(organizationMapper::toDTO)
                .toList();
    }

    public Page<OrganizationDTO> findAll(Pageable pageable) {
        final Page<Organization> organizations = organizationRepository.findAll(pageable);
        return organizations.map(organizationMapper::toDTO);
    }

    public OrganizationDTO get(final UUID id) {
        return organizationRepository.findById(id)
                .map(organizationMapper::toDTO)
                .orElseThrow(NotFoundException::new);
    }

    public UUID create(final OrganizationDTO organizationDTO) {
        final Organization organization = organizationMapper.toEntity(organizationDTO);
        return organizationRepository.save(organization).getId();
    }

    public void update(final UUID id, final OrganizationDTO organizationDTO) {
        final Organization organization = organizationRepository.findById(id)
                .orElseThrow(NotFoundException::new);
        organizationMapper.updateEntityFromDTO(organizationDTO, organization);
        organizationRepository.save(organization);
    }

    public void delete(final UUID id) {
        organizationRepository.deleteById(id);
    }

    public boolean nameExists(final String name) {
        return organizationRepository.existsByNameIgnoreCase(name);
    }

    public boolean codeExists(final String code) {
        return true;
    }

    public ReferencedWarning getReferencedWarning(final UUID id) {
        final ReferencedWarning referencedWarning = new ReferencedWarning();
        final Organization organization = organizationRepository.findById(id)
                .orElseThrow(NotFoundException::new);
//        final User organizationUser = userRepository.findFirstByOrganization(organization);
//        if (organizationUser != null) {
//            referencedWarning.setKey("organization.user.organization.referenced");
//            referencedWarning.addParam(organizationUser.getId());
//            return referencedWarning;
//        }
        return null;
    }

}
