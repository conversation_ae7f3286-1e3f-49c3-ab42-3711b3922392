package com.smaile.health.service.impl;

import com.smaile.health.mapper.RoleMapper;
import com.smaile.health.service.RoleService;
import com.smaile.health.domain.Role;
import com.smaile.health.exception.NotFoundException;
import com.smaile.health.model.RoleDTO;
import com.smaile.health.repository.RoleRepository;
import com.smaile.health.repository.UserRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@Transactional(rollbackFor = Exception.class)
public class RoleServiceImpl implements RoleService {

    private final RoleRepository roleRepository;
    private final UserRepository userRepository;
    private final RoleMapper roleMapper;

    public RoleServiceImpl(final RoleRepository roleRepository,
                           final UserRepository userRepository,
                           final RoleMapper roleMapper) {
        this.roleRepository = roleRepository;
        this.userRepository = userRepository;
        this.roleMapper = roleMapper;
    }

    public List<RoleDTO> findAll() {
        final List<Role> roles = roleRepository.findAll(Sort.by("id"));
        return roles.stream()
                .map(roleMapper::toDTO)
                .toList();
    }

    public Page<RoleDTO> findAll(Pageable pageable) {
        final Page<Role> roles = roleRepository.findAll(pageable);
        return roles.map(roleMapper::toDTO);
    }

    public RoleDTO get(final UUID id) {
        return roleRepository.findById(id)
                .map(roleMapper::toDTO)
                .orElseThrow(NotFoundException::new);
    }

    public UUID create(final RoleDTO roleDTO) {
        final Role role = roleMapper.toEntity(roleDTO);
        return roleRepository.save(role).getId();
    }

    public void update(final UUID id, final RoleDTO roleDTO) {
        final Role role = roleRepository.findById(id)
                .orElseThrow(NotFoundException::new);
        roleMapper.updateEntityFromDTO(roleDTO, role);
        roleRepository.save(role);
    }

    public void delete(final UUID id) {
//        final Role role = roleRepository.findById(id)
//                .orElseThrow(NotFoundException::new);
//        userRepository.findAllByRoles(role)
//                .forEach(user -> user.getRoles().remove(role));
//        roleRepository.delete(role);
    }

    public boolean nameExists(final String name) {
        return roleRepository.existsByNameIgnoreCase(name);
    }

}
