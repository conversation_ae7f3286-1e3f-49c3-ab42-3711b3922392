package com.smaile.health.service;

import com.smaile.health.model.RoleDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

public interface RoleService {

    List<RoleDTO> findAll();

    Page<RoleDTO> findAll(Pageable pageable);

    RoleDTO get(UUID id);

    UUID create(RoleDTO roleDTO);

    void update(UUID id, RoleDTO roleDTO);

    void delete(UUID id);

    boolean nameExists(String name);
}