package com.smaile.health.repository;

import com.smaile.health.domain.Organization;
import com.smaile.health.domain.Role;
import com.smaile.health.domain.User;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.UUID;

public interface UserRepository extends JpaRepository<User, UUID> {

    User findOneByKeycloakId(String keycloakId);

//    User findFirstByOrganization(Organization organization);

    boolean existsByUsernameIgnoreCase(String username);

    boolean existsByEmailIgnoreCase(String email);

}
