package com.smaile.health.domain;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Entity
@Table(name = "roles")
@Getter
@Setter
public class Role extends BaseEntity {

    @Id
    @Column(nullable = false, updatable = false)
    private UUID id;

    @Column(nullable = false, unique = true)
    private String name;

    @Column(nullable = false)
    private String code;

    @Column(columnDefinition = "text")
    private String description;

    @Column(nullable = false)
    private String scope;

    @Column(nullable = false, length = 100)
    private String status;

    @OneToMany(mappedBy = "role", fetch = FetchType.LAZY)
    private List<UserRoles> userRoles;

    @ManyToMany(mappedBy = "roles", fetch = FetchType.LAZY)
    private Set<Permission> permissions = new HashSet<>();

}
