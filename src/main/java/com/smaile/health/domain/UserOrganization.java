package com.smaile.health.domain;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Entity
@Table(name = "users_organizations")
@Getter
@Setter
public class UserOrganization extends ExpiredEntity {

    @Id
    @Column(nullable = false, updatable = false)
    private UUID id;

    @Column(nullable = false, length = 100)
    private String status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="user_id", nullable=false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="organization_id", nullable=false)
    private Organization organization;

    @OneToMany(mappedBy = "userOrganization", fetch = FetchType.LAZY)
    private List<UserRoles> userRoles;


}
