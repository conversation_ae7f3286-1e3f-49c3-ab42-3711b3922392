package com.smaile.health.domain;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "organizations")
@Getter
@Setter
public class Organization extends BaseEntity {

    @Id
    @Column(nullable = false, updatable = false)
    private UUID id;

    @Column()
    private String name;

    @Column()
    private String type;

    @Column()
    private String registrationNumber;

    @Column()
    private String status;

    @Column()
    private String contactPhone;

    @Column()
    private String contactEmail;

    @Column()
    private String address;

    @OneToMany(mappedBy = "organization", fetch = FetchType.LAZY)
    private List<UserOrganization> userOrganizations;

}
