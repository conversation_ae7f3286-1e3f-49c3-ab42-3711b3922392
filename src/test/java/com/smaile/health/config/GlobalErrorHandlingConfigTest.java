package com.smaile.health.config;

import com.smaile.health.config.properties.SecurityExceptionProperties;
import com.smaile.health.exception.NotFoundException;
import com.smaile.health.exception.ReferencedException;
import com.smaile.health.security.logging.SecurityContextLogger;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.context.request.ServletWebRequest;
import org.zalando.problem.Problem;
import org.zalando.problem.Status;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GlobalErrorHandlingConfigTest {

    @Mock
    private SecurityExceptionProperties securityProperties;

    @Mock
    private SecurityContextLogger securityLogger;

    @Mock
    private Environment environment;

    @Mock
    private HttpServletRequest httpRequest;

    @Mock
    private ServletWebRequest webRequest;

    @Mock
    private ConstraintViolation<Object> constraintViolation;

    @Mock
    private BindingResult bindingResult;

    @Mock
    private FieldError fieldError;

    private GlobalErrorHandlingConfig errorHandler;

    @BeforeEach
    void setUp() {
        errorHandler = new GlobalErrorHandlingConfig(securityProperties, securityLogger, environment);
        
        when(webRequest.getRequest()).thenReturn(httpRequest);
        when(httpRequest.getRequestURI()).thenReturn("/api/test");
        when(httpRequest.getMethod()).thenReturn("GET");
        when(securityProperties.getProblemTypeBaseUri()).thenReturn("https://smaile.health/problems");
        when(securityProperties.isIncludeDetailedMessages()).thenReturn(false);
        when(securityProperties.isIncludeStackTrace()).thenReturn(false);
        when(environment.getActiveProfiles()).thenReturn(new String[]{"prod"});
    }

    @Test
    void handleAuthenticationException_ShouldReturnUnauthorized() {
        // Given
        AuthenticationException exception = mock(AuthenticationException.class);

        // When
        ResponseEntity<Problem> response = errorHandler.handleAuthenticationException(exception, webRequest);

        // Then
        assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(Status.UNAUTHORIZED, response.getBody().getStatus());
        verify(securityLogger).logAuthenticationFailure(httpRequest, exception);
    }

    @Test
    void handleAccessDeniedException_ShouldReturnForbidden() {
        // Given
        AccessDeniedException exception = mock(AccessDeniedException.class);

        // When
        ResponseEntity<Problem> response = errorHandler.handleAccessDeniedException(exception, webRequest);

        // Then
        assertEquals(HttpStatus.FORBIDDEN, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(Status.FORBIDDEN, response.getBody().getStatus());
        verify(securityLogger).logAccessDenied(httpRequest, exception);
    }

    @Test
    void handleNotFoundException_ShouldReturnNotFound() {
        // Given
        NotFoundException exception = new NotFoundException("Resource not found");

        // When
        ResponseEntity<Problem> response = errorHandler.handleNotFoundException(exception, webRequest);

        // Then
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(Status.NOT_FOUND, response.getBody().getStatus());
        assertTrue(response.getBody().getType().toString().contains("not-found"));
    }

    @Test
    void handleReferencedException_ShouldReturnConflict() {
        // Given
        ReferencedException exception = new ReferencedException("Entity is referenced");

        // When
        ResponseEntity<Problem> response = errorHandler.handleReferencedException(exception, webRequest);

        // Then
        assertEquals(HttpStatus.CONFLICT, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(Status.CONFLICT, response.getBody().getStatus());
        assertTrue(response.getBody().getType().toString().contains("referenced-entity"));
    }

    @Test
    void handleConstraintViolation_ShouldReturnBadRequest() {
        // Given
        when(constraintViolation.getPropertyPath()).thenReturn(mock(jakarta.validation.Path.class));
        when(constraintViolation.getPropertyPath().toString()).thenReturn("field");
        when(constraintViolation.getMessage()).thenReturn("must not be null");
        
        ConstraintViolationException exception = new ConstraintViolationException(
                "Validation failed", Set.of(constraintViolation));

        // When
        ResponseEntity<Problem> response = errorHandler.handleConstraintViolation(exception, webRequest);

        // Then
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(Status.BAD_REQUEST, response.getBody().getStatus());
        assertTrue(response.getBody().getParameters().containsKey("field"));
    }

    @Test
    void handleMethodArgumentNotValid_ShouldReturnBadRequest() {
        // Given
        when(fieldError.getField()).thenReturn("email");
        when(fieldError.getDefaultMessage()).thenReturn("must be a valid email");
        when(bindingResult.getFieldErrors()).thenReturn(java.util.List.of(fieldError));
        
        MethodArgumentNotValidException exception = mock(MethodArgumentNotValidException.class);
        when(exception.getBindingResult()).thenReturn(bindingResult);

        // When
        ResponseEntity<Problem> response = errorHandler.handleMethodArgumentNotValid(exception, webRequest);

        // Then
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(Status.BAD_REQUEST, response.getBody().getStatus());
        assertTrue(response.getBody().getParameters().containsKey("email"));
    }

    @Test
    void buildEnhancedProblem_ShouldIncludeTimestampAndPath() {
        // Given
        NotFoundException exception = new NotFoundException("Test");

        // When
        ResponseEntity<Problem> response = errorHandler.handleNotFoundException(exception, webRequest);

        // Then
        Problem problem = response.getBody();
        assertNotNull(problem);
        assertTrue(problem.getParameters().containsKey("timestamp"));
        assertTrue(problem.getParameters().containsKey("path"));
        assertTrue(problem.getParameters().containsKey("method"));
        assertEquals("/api/test", problem.getParameters().get("path"));
        assertEquals("GET", problem.getParameters().get("method"));
    }

    @Test
    void buildEnhancedProblem_WithDetailedMessages_ShouldIncludeExceptionInfo() {
        // Given
        when(securityProperties.isIncludeDetailedMessages()).thenReturn(true);
        NotFoundException exception = new NotFoundException("Test");

        // When
        ResponseEntity<Problem> response = errorHandler.handleNotFoundException(exception, webRequest);

        // Then
        Problem problem = response.getBody();
        assertNotNull(problem);
        assertTrue(problem.getParameters().containsKey("exception"));
        assertEquals("NotFoundException", problem.getParameters().get("exception"));
    }

    @Test
    void buildEnhancedProblem_WithStackTrace_ShouldIncludeTrace() {
        // Given
        when(securityProperties.isIncludeDetailedMessages()).thenReturn(true);
        when(securityProperties.isIncludeStackTrace()).thenReturn(true);
        NotFoundException exception = new NotFoundException("Test");

        // When
        ResponseEntity<Problem> response = errorHandler.handleNotFoundException(exception, webRequest);

        // Then
        Problem problem = response.getBody();
        assertNotNull(problem);
        assertTrue(problem.getParameters().containsKey("trace"));
    }

    @Test
    void shouldIncludeDetailedMessages_WithDevProfile_ShouldReturnTrue() {
        // Given
        when(environment.getActiveProfiles()).thenReturn(new String[]{"dev"});
        when(securityProperties.isIncludeDetailedMessages()).thenReturn(false);
        NotFoundException exception = new NotFoundException("Test");

        // When
        ResponseEntity<Problem> response = errorHandler.handleNotFoundException(exception, webRequest);

        // Then
        Problem problem = response.getBody();
        assertNotNull(problem);
        assertTrue(problem.getParameters().containsKey("exception"));
    }

    @Test
    void shouldIncludeDetailedMessages_WithTestProfile_ShouldReturnTrue() {
        // Given
        when(environment.getActiveProfiles()).thenReturn(new String[]{"test"});
        when(securityProperties.isIncludeDetailedMessages()).thenReturn(false);
        NotFoundException exception = new NotFoundException("Test");

        // When
        ResponseEntity<Problem> response = errorHandler.handleNotFoundException(exception, webRequest);

        // Then
        Problem problem = response.getBody();
        assertNotNull(problem);
        assertTrue(problem.getParameters().containsKey("exception"));
    }

    @Test
    void sanitizeErrorMessage_WithSensitiveInfo_ShouldMaskSecrets() {
        // Given
        ReferencedException exception = new ReferencedException("Error with password=secret123");

        // When
        ResponseEntity<Problem> response = errorHandler.handleReferencedException(exception, webRequest);

        // Then
        Problem problem = response.getBody();
        assertNotNull(problem);
        String message = (String) problem.getParameters().get("message");
        assertFalse(message.contains("secret123"));
        assertTrue(message.contains("password=***"));
    }

    @Test
    void sanitizeErrorMessage_WithLongMessage_ShouldTruncate() {
        // Given
        String longMessage = "A".repeat(250);
        ReferencedException exception = new ReferencedException(longMessage);

        // When
        ResponseEntity<Problem> response = errorHandler.handleReferencedException(exception, webRequest);

        // Then
        Problem problem = response.getBody();
        assertNotNull(problem);
        String message = (String) problem.getParameters().get("message");
        assertTrue(message.length() <= 203); // 200 + "..."
    }

    @Test
    void extractHttpRequest_WithNonServletWebRequest_ShouldThrowException() {
        // Given
        org.springframework.web.context.request.NativeWebRequest nonServletRequest = 
                mock(org.springframework.web.context.request.NativeWebRequest.class);
        NotFoundException exception = new NotFoundException("Test");

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> 
                errorHandler.handleNotFoundException(exception, nonServletRequest));
    }
}
