package com.smaile.health.security.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.smaile.health.config.properties.SecurityExceptionProperties;
import com.smaile.health.security.UserSecurityContext;
import com.smaile.health.security.logging.SecurityContextLogger;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.zalando.problem.Problem;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EnterpriseAccessDeniedHandlerTest {

    @Mock
    private SecurityExceptionProperties properties;

    @Mock
    private SecurityContextLogger securityLogger;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Mock
    private AccessDeniedException accessDeniedException;

    @Mock
    private SecurityContext securityContext;

    @Mock
    private Authentication authentication;

    @Mock
    private UserSecurityContext userSecurityContext;

    private EnterpriseAccessDeniedHandler accessDeniedHandler;
    private StringWriter responseWriter;
    private PrintWriter printWriter;

    @BeforeEach
    void setUp() throws Exception {
        accessDeniedHandler = new EnterpriseAccessDeniedHandler(properties, securityLogger, objectMapper);
        
        responseWriter = new StringWriter();
        printWriter = new PrintWriter(responseWriter);
        
        when(response.getWriter()).thenReturn(printWriter);
        when(request.getRequestURI()).thenReturn("/api/admin");
        when(request.getMethod()).thenReturn("POST");
        
        // Setup default properties
        SecurityExceptionProperties.AccessDeniedErrorConfig accessConfig = 
                new SecurityExceptionProperties.AccessDeniedErrorConfig();
        accessConfig.setStatusCode(403);
        accessConfig.setTitle("Access Denied");
        accessConfig.setDetail("You do not have sufficient permissions");
        accessConfig.setType("access-denied");
        
        when(properties.getAccessDenied()).thenReturn(accessConfig);
        when(properties.getProblemTypeBaseUri()).thenReturn("https://smaile.health/problems");
        when(properties.getCustomHeaders()).thenReturn(Map.of(
                "X-Content-Type-Options", "nosniff",
                "X-Frame-Options", "DENY"
        ));
        when(properties.isIncludeDetailedMessages()).thenReturn(false);
        when(properties.isIncludeStackTrace()).thenReturn(false);
    }

    @Test
    void handle_ShouldLogAccessDenied() throws Exception {
        // Given
        when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn("{}");

        // When
        accessDeniedHandler.handle(request, response, accessDeniedException);

        // Then
        verify(securityLogger).logAccessDenied(request, accessDeniedException);
    }

    @Test
    void handle_ShouldSetCorrectResponseStatus() throws Exception {
        // Given
        when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn("{}");

        // When
        accessDeniedHandler.handle(request, response, accessDeniedException);

        // Then
        verify(response).setStatus(403);
    }

    @Test
    void handle_ShouldSetCorrectContentType() throws Exception {
        // Given
        when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn("{}");

        // When
        accessDeniedHandler.handle(request, response, accessDeniedException);

        // Then
        verify(response).setContentType(MediaType.APPLICATION_PROBLEM_JSON_VALUE);
        verify(response).setCharacterEncoding("UTF-8");
    }

    @Test
    void handle_ShouldSetSecurityHeaders() throws Exception {
        // Given
        when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn("{}");

        // When
        accessDeniedHandler.handle(request, response, accessDeniedException);

        // Then
        verify(response).setHeader("X-Content-Type-Options", "nosniff");
        verify(response).setHeader("X-Frame-Options", "DENY");
        verify(response).setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        verify(response).setHeader("Pragma", "no-cache");
        verify(response).setHeader("Expires", "0");
    }

    @Test
    void handle_ShouldWriteProblemResponse() throws Exception {
        // Given
        String expectedJson = "{\"type\":\"access-denied\"}";
        when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn(expectedJson);

        // When
        accessDeniedHandler.handle(request, response, accessDeniedException);

        // Then
        verify(objectMapper).writeValueAsString(any(Problem.class));
        assertEquals(expectedJson, responseWriter.toString());
    }

    @Test
    void handle_WithDetailedMessages_ShouldIncludeUserContext() throws Exception {
        // Given
        when(properties.isIncludeDetailedMessages()).thenReturn(true);
        when(userSecurityContext.getUserId()).thenReturn("user123");
        when(userSecurityContext.getPrimaryRole()).thenReturn("USER");
        when(userSecurityContext.getPermissions()).thenReturn(Set.of("READ", "WRITE"));
        when(authentication.isAuthenticated()).thenReturn(true);
        when(authentication.getDetails()).thenReturn(userSecurityContext);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        
        try (MockedStatic<SecurityContextHolder> mockedSecurityContextHolder = 
                mockStatic(SecurityContextHolder.class)) {
            mockedSecurityContextHolder.when(SecurityContextHolder::getContext)
                    .thenReturn(securityContext);
            
            when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn("{}");

            // When
            accessDeniedHandler.handle(request, response, accessDeniedException);

            // Then
            verify(objectMapper).writeValueAsString(argThat(problem -> {
                Problem p = (Problem) problem;
                return p.getParameters().containsKey("userId") &&
                       p.getParameters().containsKey("userRole") &&
                       p.getParameters().containsKey("permissions");
            }));
        }
    }

    @Test
    void handle_WithStackTrace_ShouldIncludeTrace() throws Exception {
        // Given
        when(properties.isIncludeDetailedMessages()).thenReturn(true);
        when(properties.isIncludeStackTrace()).thenReturn(true);
        when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn("{}");

        // When
        accessDeniedHandler.handle(request, response, accessDeniedException);

        // Then
        verify(objectMapper).writeValueAsString(argThat(problem -> {
            Problem p = (Problem) problem;
            return p.getParameters().containsKey("trace");
        }));
    }

    @Test
    void handle_ShouldSanitizeErrorMessages() throws Exception {
        // Given
        when(properties.isIncludeDetailedMessages()).thenReturn(true);
        when(accessDeniedException.getMessage()).thenReturn("Access denied for token=abc123");
        when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn("{}");

        // When
        accessDeniedHandler.handle(request, response, accessDeniedException);

        // Then
        verify(objectMapper).writeValueAsString(argThat(problem -> {
            Problem p = (Problem) problem;
            String detail = p.getDetail();
            return !detail.contains("abc123") && detail.contains("token=***");
        }));
    }

    @Test
    void handle_ShouldIncludeTimestampAndPath() throws Exception {
        // Given
        when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn("{}");

        // When
        accessDeniedHandler.handle(request, response, accessDeniedException);

        // Then
        verify(objectMapper).writeValueAsString(argThat(problem -> {
            Problem p = (Problem) problem;
            return p.getParameters().containsKey("timestamp") &&
                   p.getParameters().containsKey("path") &&
                   p.getParameters().containsKey("method");
        }));
    }

    @Test
    void handle_WithoutAuthentication_ShouldNotIncludeUserContext() throws Exception {
        // Given
        when(properties.isIncludeDetailedMessages()).thenReturn(true);
        when(securityContext.getAuthentication()).thenReturn(null);
        
        try (MockedStatic<SecurityContextHolder> mockedSecurityContextHolder = 
                mockStatic(SecurityContextHolder.class)) {
            mockedSecurityContextHolder.when(SecurityContextHolder::getContext)
                    .thenReturn(securityContext);
            
            when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn("{}");

            // When
            accessDeniedHandler.handle(request, response, accessDeniedException);

            // Then
            verify(objectMapper).writeValueAsString(argThat(problem -> {
                Problem p = (Problem) problem;
                return !p.getParameters().containsKey("userId");
            }));
        }
    }
}
