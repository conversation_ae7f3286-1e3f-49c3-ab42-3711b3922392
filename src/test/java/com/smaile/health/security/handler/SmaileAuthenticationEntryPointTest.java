package com.smaile.health.security.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.smaile.health.config.properties.SecurityExceptionProperties;
import com.smaile.health.security.logging.SecurityContextLogger;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.zalando.problem.Problem;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SmaileAuthenticationEntryPointTest {

    @Mock
    private SecurityExceptionProperties properties;

    @Mock
    private SecurityContextLogger securityLogger;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Mock
    private AuthenticationException authException;

    private SmaileAuthenticationEntryPoint entryPoint;
    private StringWriter responseWriter;
    private PrintWriter printWriter;

    @BeforeEach
    void setUp() throws Exception {
        entryPoint = new SmaileAuthenticationEntryPoint(properties, securityLogger, objectMapper);
        
        responseWriter = new StringWriter();
        printWriter = new PrintWriter(responseWriter);
        
        when(response.getWriter()).thenReturn(printWriter);
        when(request.getRequestURI()).thenReturn("/api/test");
        when(request.getMethod()).thenReturn("GET");
        
        // Setup default properties
        SecurityExceptionProperties.AuthenticationErrorConfig authConfig = 
                new SecurityExceptionProperties.AuthenticationErrorConfig();
        authConfig.setStatusCode(401);
        authConfig.setTitle("Authentication Required");
        authConfig.setDetail("Valid authentication credentials are required");
        authConfig.setType("authentication-required");
        
        when(properties.getAuthentication()).thenReturn(authConfig);
        when(properties.getProblemTypeBaseUri()).thenReturn("https://smaile.health/problems");
        when(properties.getCustomHeaders()).thenReturn(Map.of(
                "X-Content-Type-Options", "nosniff",
                "X-Frame-Options", "DENY"
        ));
        when(properties.isIncludeDetailedMessages()).thenReturn(false);
        when(properties.isIncludeStackTrace()).thenReturn(false);
    }

    @Test
    void commence_ShouldLogAuthenticationFailure() throws Exception {
        // Given
        when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn("{}");

        // When
        entryPoint.commence(request, response, authException);

        // Then
        verify(securityLogger).logAuthenticationFailure(request, authException);
    }

    @Test
    void commence_ShouldSetCorrectResponseStatus() throws Exception {
        // Given
        when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn("{}");

        // When
        entryPoint.commence(request, response, authException);

        // Then
        verify(response).setStatus(401);
    }

    @Test
    void commence_ShouldSetCorrectContentType() throws Exception {
        // Given
        when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn("{}");

        // When
        entryPoint.commence(request, response, authException);

        // Then
        verify(response).setContentType(MediaType.APPLICATION_PROBLEM_JSON_VALUE);
        verify(response).setCharacterEncoding("UTF-8");
    }

    @Test
    void commence_ShouldSetSecurityHeaders() throws Exception {
        // Given
        when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn("{}");

        // When
        entryPoint.commence(request, response, authException);

        // Then
        verify(response).setHeader("X-Content-Type-Options", "nosniff");
        verify(response).setHeader("X-Frame-Options", "DENY");
        verify(response).setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        verify(response).setHeader("Pragma", "no-cache");
        verify(response).setHeader("Expires", "0");
    }

    @Test
    void commence_ShouldWriteProblemResponse() throws Exception {
        // Given
        String expectedJson = "{\"type\":\"authentication-required\"}";
        when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn(expectedJson);

        // When
        entryPoint.commence(request, response, authException);

        // Then
        verify(objectMapper).writeValueAsString(any(Problem.class));
        assertEquals(expectedJson, responseWriter.toString());
    }

    @Test
    void commence_WithDetailedMessages_ShouldIncludeExceptionInfo() throws Exception {
        // Given
        when(properties.isIncludeDetailedMessages()).thenReturn(true);
        when(authException.getMessage()).thenReturn("Invalid credentials");
        when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn("{}");

        // When
        entryPoint.commence(request, response, authException);

        // Then
        verify(objectMapper).writeValueAsString(argThat(problem -> {
            Problem p = (Problem) problem;
            return p.getParameters().containsKey("exception");
        }));
    }

    @Test
    void commence_WithStackTrace_ShouldIncludeTrace() throws Exception {
        // Given
        when(properties.isIncludeDetailedMessages()).thenReturn(true);
        when(properties.isIncludeStackTrace()).thenReturn(true);
        when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn("{}");

        // When
        entryPoint.commence(request, response, authException);

        // Then
        verify(objectMapper).writeValueAsString(argThat(problem -> {
            Problem p = (Problem) problem;
            return p.getParameters().containsKey("trace");
        }));
    }

    @Test
    void commence_ShouldSanitizeErrorMessages() throws Exception {
        // Given
        when(properties.isIncludeDetailedMessages()).thenReturn(true);
        when(authException.getMessage()).thenReturn("Authentication failed for password=secret123");
        when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn("{}");

        // When
        entryPoint.commence(request, response, authException);

        // Then
        verify(objectMapper).writeValueAsString(argThat(problem -> {
            Problem p = (Problem) problem;
            String detail = p.getDetail();
            return !detail.contains("secret123") && detail.contains("password=***");
        }));
    }

    @Test
    void commence_WithLongErrorMessage_ShouldTruncate() throws Exception {
        // Given
        when(properties.isIncludeDetailedMessages()).thenReturn(true);
        String longMessage = "A".repeat(250);
        when(authException.getMessage()).thenReturn(longMessage);
        when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn("{}");

        // When
        entryPoint.commence(request, response, authException);

        // Then
        verify(objectMapper).writeValueAsString(argThat(problem -> {
            Problem p = (Problem) problem;
            String detail = p.getDetail();
            return detail.length() <= 203; // 200 + "..."
        }));
    }

    @Test
    void commence_ShouldIncludeTimestampAndPath() throws Exception {
        // Given
        when(objectMapper.writeValueAsString(any(Problem.class))).thenReturn("{}");

        // When
        entryPoint.commence(request, response, authException);

        // Then
        verify(objectMapper).writeValueAsString(argThat(problem -> {
            Problem p = (Problem) problem;
            return p.getParameters().containsKey("timestamp") &&
                   p.getParameters().containsKey("path") &&
                   p.getParameters().containsKey("method");
        }));
    }
}
