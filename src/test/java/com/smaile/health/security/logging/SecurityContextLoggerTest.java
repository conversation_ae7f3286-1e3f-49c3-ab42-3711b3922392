package com.smaile.health.security.logging;

import com.smaile.health.config.properties.SecurityExceptionProperties;
import com.smaile.health.security.UserSecurityContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SecurityContextLoggerTest {

    @Mock
    private SecurityExceptionProperties properties;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpSession session;

    @Mock
    private AuthenticationException authException;

    @Mock
    private AccessDeniedException accessDeniedException;

    @Mock
    private SecurityContext securityContext;

    @Mock
    private Authentication authentication;

    @Mock
    private UserSecurityContext userSecurityContext;

    private SecurityContextLogger securityLogger;

    @BeforeEach
    void setUp() {
        securityLogger = new SecurityContextLogger(properties);
        
        when(request.getRequestURI()).thenReturn("/api/test");
        when(request.getMethod()).thenReturn("GET");
        when(request.getRemoteAddr()).thenReturn("***********");
        when(request.getHeader("User-Agent")).thenReturn("Mozilla/5.0");
        when(request.getSession(false)).thenReturn(session);
        when(session.getId()).thenReturn("session123456789");
    }

    @Test
    void logAuthenticationFailure_WhenLoggingDisabled_ShouldNotLog() {
        // Given
        when(properties.isEnableLogging()).thenReturn(false);

        // When
        securityLogger.logAuthenticationFailure(request, authException);

        // Then
        // No logging should occur - this is verified by the absence of any log output
        verify(properties).isEnableLogging();
        verifyNoMoreInteractions(properties);
    }

    @Test
    void logAuthenticationFailure_WhenLoggingEnabled_ShouldLog() {
        // Given
        when(properties.isEnableLogging()).thenReturn(true);
        when(properties.getLogLevel()).thenReturn("WARN");

        // When
        securityLogger.logAuthenticationFailure(request, authException);

        // Then
        verify(properties).isEnableLogging();
        verify(properties).getLogLevel();
    }

    @Test
    void logAccessDenied_WhenLoggingDisabled_ShouldNotLog() {
        // Given
        when(properties.isEnableLogging()).thenReturn(false);

        // When
        securityLogger.logAccessDenied(request, accessDeniedException);

        // Then
        verify(properties).isEnableLogging();
        verifyNoMoreInteractions(properties);
    }

    @Test
    void logAccessDenied_WithAuthenticatedUser_ShouldIncludeUserId() {
        // Given
        when(properties.isEnableLogging()).thenReturn(true);
        when(properties.getLogLevel()).thenReturn("WARN");
        when(userSecurityContext.getUserId()).thenReturn("user123");
        when(authentication.isAuthenticated()).thenReturn(true);
        when(authentication.getDetails()).thenReturn(userSecurityContext);
        when(securityContext.getAuthentication()).thenReturn(authentication);

        try (MockedStatic<SecurityContextHolder> mockedSecurityContextHolder = 
                mockStatic(SecurityContextHolder.class)) {
            mockedSecurityContextHolder.when(SecurityContextHolder::getContext)
                    .thenReturn(securityContext);

            // When
            securityLogger.logAccessDenied(request, accessDeniedException);

            // Then
            verify(properties).isEnableLogging();
            verify(properties).getLogLevel();
        }
    }

    @Test
    void logAccessDenied_WithoutAuthentication_ShouldUseAnonymous() {
        // Given
        when(properties.isEnableLogging()).thenReturn(true);
        when(properties.getLogLevel()).thenReturn("WARN");
        when(securityContext.getAuthentication()).thenReturn(null);

        try (MockedStatic<SecurityContextHolder> mockedSecurityContextHolder = 
                mockStatic(SecurityContextHolder.class)) {
            mockedSecurityContextHolder.when(SecurityContextHolder::getContext)
                    .thenReturn(securityContext);

            // When
            securityLogger.logAccessDenied(request, accessDeniedException);

            // Then
            verify(properties).isEnableLogging();
            verify(properties).getLogLevel();
        }
    }

    @Test
    void logSecurityEvent_WhenLoggingDisabled_ShouldNotLog() {
        // Given
        when(properties.isEnableLogging()).thenReturn(false);

        // When
        securityLogger.logSecurityEvent(request, "Test event", "INFO");

        // Then
        verify(properties).isEnableLogging();
        verifyNoMoreInteractions(properties);
    }

    @Test
    void logSecurityEvent_WhenLoggingEnabled_ShouldLog() {
        // Given
        when(properties.isEnableLogging()).thenReturn(true);

        // When
        securityLogger.logSecurityEvent(request, "Test security event", "INFO");

        // Then
        verify(properties).isEnableLogging();
    }

    @Test
    void extractClientIp_WithXForwardedFor_ShouldUseForwardedIp() {
        // Given
        when(request.getHeader("X-Forwarded-For")).thenReturn("********, ***********");
        when(properties.isEnableLogging()).thenReturn(true);
        when(properties.getLogLevel()).thenReturn("WARN");

        // When
        securityLogger.logAuthenticationFailure(request, authException);

        // Then
        verify(request).getHeader("X-Forwarded-For");
    }

    @Test
    void extractClientIp_WithXRealIp_ShouldUseRealIp() {
        // Given
        when(request.getHeader("X-Forwarded-For")).thenReturn(null);
        when(request.getHeader("X-Real-IP")).thenReturn("********");
        when(properties.isEnableLogging()).thenReturn(true);
        when(properties.getLogLevel()).thenReturn("WARN");

        // When
        securityLogger.logAuthenticationFailure(request, authException);

        // Then
        verify(request).getHeader("X-Real-IP");
    }

    @Test
    void extractClientIp_WithoutProxyHeaders_ShouldUseRemoteAddr() {
        // Given
        when(request.getHeader("X-Forwarded-For")).thenReturn(null);
        when(request.getHeader("X-Real-IP")).thenReturn(null);
        when(request.getHeader("X-Originating-IP")).thenReturn(null);
        when(request.getHeader("CF-Connecting-IP")).thenReturn(null);
        when(properties.isEnableLogging()).thenReturn(true);
        when(properties.getLogLevel()).thenReturn("WARN");

        // When
        securityLogger.logAuthenticationFailure(request, authException);

        // Then
        verify(request).getRemoteAddr();
    }

    @Test
    void sanitizeUri_WithMaliciousContent_ShouldSanitize() {
        // Given
        when(request.getRequestURI()).thenReturn("/api/test\r\n\tmalicious");
        when(properties.isEnableLogging()).thenReturn(true);
        when(properties.getLogLevel()).thenReturn("WARN");

        // When
        securityLogger.logAuthenticationFailure(request, authException);

        // Then
        // The URI should be sanitized (verified through logging behavior)
        verify(properties).isEnableLogging();
    }

    @Test
    void sanitizeUserAgent_WithMaliciousContent_ShouldSanitize() {
        // Given
        when(request.getHeader("User-Agent")).thenReturn("Mozilla\r\nmalicious\tcontent");
        when(properties.isEnableLogging()).thenReturn(true);
        when(properties.getLogLevel()).thenReturn("WARN");

        // When
        securityLogger.logAuthenticationFailure(request, authException);

        // Then
        // The user agent should be sanitized (verified through logging behavior)
        verify(properties).isEnableLogging();
    }

    @Test
    void sanitizeSessionId_WithLongSessionId_ShouldTruncate() {
        // Given
        when(session.getId()).thenReturn("verylongsessionid123456789");
        when(properties.isEnableLogging()).thenReturn(true);
        when(properties.getLogLevel()).thenReturn("WARN");

        // When
        securityLogger.logAuthenticationFailure(request, authException);

        // Then
        // The session ID should be truncated for privacy
        verify(properties).isEnableLogging();
    }

    @Test
    void logWithLevel_WithErrorLevel_ShouldLogAsError() {
        // Given
        when(properties.isEnableLogging()).thenReturn(true);
        when(properties.getLogLevel()).thenReturn("ERROR");

        // When
        securityLogger.logAuthenticationFailure(request, authException);

        // Then
        verify(properties).getLogLevel();
    }

    @Test
    void logWithLevel_WithDebugLevel_ShouldLogAsDebug() {
        // Given
        when(properties.isEnableLogging()).thenReturn(true);
        when(properties.getLogLevel()).thenReturn("DEBUG");

        // When
        securityLogger.logAuthenticationFailure(request, authException);

        // Then
        verify(properties).getLogLevel();
    }

    @Test
    void logWithLevel_WithInvalidLevel_ShouldDefaultToWarn() {
        // Given
        when(properties.isEnableLogging()).thenReturn(true);
        when(properties.getLogLevel()).thenReturn("INVALID");

        // When
        securityLogger.logAuthenticationFailure(request, authException);

        // Then
        verify(properties).getLogLevel();
    }
}
